#!/bin/bash

# Test Loki configuration validity

echo "🔍 Testing Loki configuration..."

# Stop Loki first
echo "🛑 Stopping Loki..."
docker-compose stop loki

# Remove any existing Loki data
echo "🧹 Cleaning Loki data..."
docker volume rm voice-agent_loki_data 2>/dev/null || true

# Test the configuration by starting Loki
echo "🚀 Testing Loki with current configuration..."
docker-compose up -d loki

# Wait and check for errors
echo "⏳ Waiting for Loki to start..."
sleep 10

# Check for configuration errors
echo "📋 Checking for configuration errors..."
CONFIG_ERRORS=$(docker-compose logs loki --tail=20 | grep -i "failed parsing\|yaml\|unmarshal\|error" || echo "")

if [ -n "$CONFIG_ERRORS" ]; then
    echo "❌ Configuration errors found:"
    echo "$CONFIG_ERRORS"
    echo ""
    echo "💡 Full logs:"
    docker-compose logs loki --tail=30
    exit 1
else
    echo "✅ No configuration errors found"
fi

# Test if Loki is ready
echo "🔍 Testing Loki readiness..."
for i in {1..15}; do
    if curl -s http://localhost:3100/ready > /dev/null 2>&1; then
        echo "✅ Loki is ready and responding!"
        break
    else
        echo "⏳ Waiting for Loki... ($i/15)"
        sleep 2
    fi
done

# Test API endpoints
echo "🔍 Testing Loki API endpoints..."

# Test labels endpoint
if curl -s "http://localhost:3100/loki/api/v1/labels" > /dev/null 2>&1; then
    echo "✅ Labels API working"
else
    echo "⚠️  Labels API not ready"
fi

# Test metrics endpoint
if curl -s "http://localhost:3100/metrics" > /dev/null 2>&1; then
    echo "✅ Metrics API working"
else
    echo "⚠️  Metrics API not ready"
fi

echo ""
echo "🎉 Loki configuration test completed!"
echo "📊 Loki is running at: http://localhost:3100"
echo "🔍 Ready endpoint: http://localhost:3100/ready"
echo "📋 Labels endpoint: http://localhost:3100/loki/api/v1/labels"
echo ""
echo "🚀 Next step: Run ./fix-logging-issues.sh to complete the setup"
