@echo off
REM Comprehensive fix for all logging issues

echo 🔧 Fixing all logging issues...

REM Stop all services
echo 🛑 Stopping all services...
docker-compose down --remove-orphans

REM Clean up Loki data to fix timestamp issues
echo 🧹 Cleaning up Loki data (fixes timestamp issues)...
docker volume rm voice-agent_loki_data 2>nul

REM Ensure log files exist and are accessible
echo 📝 Setting up log files...
if not exist logs mkdir logs
if not exist salon_ai.log echo. > salon_ai.log
if not exist logs\salon_ai_structured.log echo. > logs\salon_ai_structured.log

REM Start services with fixed configuration
echo 🚀 Starting services with fixes...
docker-compose up -d

REM Wait for services to start
echo ⏳ Waiting for services to initialize...
timeout /t 20 /nobreak >nul

REM Check Loki
echo 🔍 Checking Loki...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready
        goto :loki_ready
    ) else (
        echo ⏳ Loki starting... (%%i/10)
        timeout /t 3 /nobreak >nul
    )
)
:loki_ready

REM Check Promtail
echo 🔍 Checking Promtail...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:9080/targets >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Promtail is ready
        goto :promtail_ready
    ) else (
        echo ⏳ Promtail starting... (%%i/10)
        timeout /t 3 /nobreak >nul
    )
)
:promtail_ready

REM Check Grafana
echo 🔍 Checking Grafana...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3001/api/health >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Grafana is ready
        goto :grafana_ready
    ) else (
        echo ⏳ Grafana starting... (%%i/10)
        timeout /t 3 /nobreak >nul
    )
)
:grafana_ready

REM Generate test logs
echo 🧪 Generating test logs...
python test_logging.py

REM Wait for logs to be processed
echo ⏳ Waiting for logs to be processed...
timeout /t 10 /nobreak >nul

REM Check available labels
echo 📋 Available labels in Loki:
curl -s "http://localhost:3100/loki/api/v1/labels"

echo.
echo 🎉 Logging fixes applied!
echo.
echo 📊 Access URLs:
echo    Grafana:    http://localhost:3001 (admin/admin)
echo    Loki API:   http://localhost:3100
echo    Promtail:   http://localhost:9080
echo.
echo 🔍 To check logs in Grafana:
echo    1. Go to http://localhost:3001
echo    2. Navigate to Explore
echo    3. Select Loki datasource
echo    4. Try query: {job="salon-ai-structured"}
echo.
echo 🧪 Generate more test logs: python test_logging.py

pause
