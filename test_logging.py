#!/usr/bin/env python3
"""
Test script for Loki logging setup
This script generates various types of log messages to test the logging infrastructure
"""

import time
import random
from config.logging import setup_logging, get_logger

def test_logging():
    """Test the logging setup with various log levels and structured data"""
    
    # Setup logging
    setup_logging()
    
    # Get loggers for different components
    main_logger = get_logger('test_main')
    db_logger = get_logger('test_database')
    voice_logger = get_logger('test_voice_agent')
    metrics_logger = get_logger('test_metrics')
    
    print("Testing Loki logging setup...")
    print("Check Grafana at http://localhost:3001 to view logs")
    print("Generating test logs...")
    
    # Test basic logging
    main_logger.info("Test logging setup started")
    
    # Test structured logging with different components
    components = ['database', 'voice_agent', 'livekit', 'metrics']
    events = ['startup', 'shutdown', 'user_action', 'error_recovery', 'performance_check']
    statuses = ['success', 'error', 'warning', 'in_progress']
    
    for i in range(20):
        component = random.choice(components)
        event = random.choice(events)
        status = random.choice(statuses)
        
        if component == 'database':
            logger = db_logger
        elif component == 'voice_agent':
            logger = voice_logger
        elif component == 'metrics':
            logger = metrics_logger
        else:
            logger = main_logger
        
        # Generate different types of log messages
        if status == 'error':
            logger.error(f"Test error in {component}", extra={
                "event": event,
                "component": component,
                "status": status,
                "error_type": "TestError",
                "error_code": random.randint(1000, 9999),
                "retry_count": random.randint(0, 3)
            })
        elif status == 'warning':
            logger.warning(f"Test warning in {component}", extra={
                "event": event,
                "component": component,
                "status": status,
                "threshold_value": random.randint(50, 100),
                "current_value": random.randint(80, 120)
            })
        else:
            logger.info(f"Test {event} in {component}", extra={
                "event": event,
                "component": component,
                "status": status,
                "duration_ms": random.randint(10, 1000),
                "user_id": f"test_user_{random.randint(1, 100)}"
            })
        
        # Add some delay to spread out the logs
        time.sleep(0.5)
    
    # Test specific scenarios
    print("Testing specific scenarios...")
    
    # Application startup sequence
    main_logger.info("Application startup initiated", extra={
        "event": "application_startup",
        "component": "main",
        "status": "in_progress",
        "version": "1.0.0"
    })
    
    db_logger.info("Database connection established", extra={
        "event": "database_connection",
        "component": "database",
        "status": "success",
        "connection_pool_size": 10,
        "connection_timeout": 30
    })
    
    voice_logger.info("Voice agent initialized", extra={
        "event": "voice_agent_init",
        "component": "voice_agent",
        "status": "success",
        "model": "gpt-4",
        "voice_provider": "elevenlabs"
    })
    
    # Simulate some user interactions
    for i in range(5):
        user_id = f"user_{random.randint(1000, 9999)}"
        phone_number = f"+1{random.randint(**********, **********)}"
        
        voice_logger.info("Customer call started", extra={
            "event": "call_started",
            "component": "voice_agent",
            "status": "success",
            "user_id": user_id,
            "phone_number": phone_number,
            "call_duration": 0
        })
        
        time.sleep(1)
        
        voice_logger.info("Customer call ended", extra={
            "event": "call_ended",
            "component": "voice_agent",
            "status": "success",
            "user_id": user_id,
            "phone_number": phone_number,
            "call_duration": random.randint(60, 600),
            "appointment_created": random.choice([True, False])
        })
    
    # Test error scenarios
    db_logger.error("Database connection lost", extra={
        "event": "database_error",
        "component": "database",
        "status": "error",
        "error_type": "ConnectionError",
        "error_message": "Connection timeout after 30 seconds",
        "retry_attempt": 1
    })
    
    voice_logger.error("Voice synthesis failed", extra={
        "event": "voice_synthesis_error",
        "component": "voice_agent",
        "status": "error",
        "error_type": "APIError",
        "provider": "elevenlabs",
        "error_code": 429,
        "rate_limit_exceeded": True
    })
    
    # Test metrics logging
    metrics_logger.info("Performance metrics collected", extra={
        "event": "metrics_collection",
        "component": "metrics",
        "status": "success",
        "cpu_usage": random.randint(20, 80),
        "memory_usage": random.randint(30, 90),
        "active_calls": random.randint(0, 10),
        "response_time_ms": random.randint(100, 500)
    })
    
    main_logger.info("Test logging completed", extra={
        "event": "test_completed",
        "component": "test",
        "status": "success",
        "total_logs_generated": 30,
        "test_duration": 30
    })
    
    print("Test logging completed!")
    print("Check the following in Grafana:")
    print("1. Go to http://localhost:3001")
    print("2. Navigate to 'Salon AI - Logs Dashboard'")
    print("3. Check the 'Recent Logs' panel for all generated logs")
    print("4. Check 'Error Logs' and 'Warning Logs' panels for filtered views")
    print("5. Use Explore to run custom queries like: {job=\"salon-ai-structured\"}")

if __name__ == "__main__":
    test_logging()
