#!/bin/bash

# Debug script to troubleshoot logging pipeline

echo "🔍 Debugging Loki Logging Pipeline..."
echo "=================================="

# Check if services are running
echo "1. Checking service status..."
echo "------------------------------"
docker-compose ps

echo ""
echo "2. Checking log files exist..."
echo "------------------------------"
if [ -f "salon_ai.log" ]; then
    echo "✅ salon_ai.log exists ($(wc -l < salon_ai.log) lines)"
    echo "Last 3 lines:"
    tail -3 salon_ai.log
else
    echo "❌ salon_ai.log not found"
fi

if [ -f "logs/salon_ai_structured.log" ]; then
    echo "✅ logs/salon_ai_structured.log exists ($(wc -l < logs/salon_ai_structured.log) lines)"
    echo "Last 3 lines:"
    tail -3 logs/salon_ai_structured.log
else
    echo "❌ logs/salon_ai_structured.log not found"
fi

echo ""
echo "3. Testing Loki API..."
echo "---------------------"
# Test Loki ready endpoint
if curl -s http://localhost:3100/ready > /dev/null; then
    echo "✅ Loki is ready"
else
    echo "❌ Loki is not ready"
fi

# Test Loki labels endpoint
echo "Checking Loki labels:"
curl -s "http://localhost:3100/loki/api/v1/labels" | jq . 2>/dev/null || curl -s "http://localhost:3100/loki/api/v1/labels"

echo ""
echo "4. Checking Promtail status..."
echo "------------------------------"
if curl -s http://localhost:9080/targets > /dev/null; then
    echo "✅ Promtail is responding"
    echo "Promtail targets:"
    curl -s http://localhost:9080/targets | jq . 2>/dev/null || curl -s http://localhost:9080/targets
else
    echo "❌ Promtail is not responding"
fi

echo ""
echo "5. Checking recent logs from services..."
echo "----------------------------------------"
echo "Loki logs (last 10 lines):"
docker-compose logs loki --tail=10

echo ""
echo "Promtail logs (last 10 lines):"
docker-compose logs promtail --tail=10

echo ""
echo "6. Testing log ingestion..."
echo "---------------------------"
# Generate a test log entry
echo "$(date '+%Y-%m-%d %H:%M:%S,%3N') - test.debug - INFO - Debug test log entry" >> salon_ai.log
echo '{"timestamp": '$(date +%s)', "level": "INFO", "logger": "test.debug", "message": "Debug structured test log", "service": "salon-ai-voice-agent"}' >> logs/salon_ai_structured.log

echo "Added test log entries"

# Wait a moment for Promtail to pick them up
sleep 5

echo ""
echo "7. Querying Loki for recent logs..."
echo "-----------------------------------"
# Query Loki for recent logs
echo "Querying all jobs:"
curl -s "http://localhost:3100/loki/api/v1/query_range?query={job=~\".*\"}&start=$(date -d '1 hour ago' +%s)000000000&end=$(date +%s)000000000" | jq '.data.result | length' 2>/dev/null || echo "Query failed or no jq"

echo ""
echo "Querying salon-ai-structured job:"
curl -s "http://localhost:3100/loki/api/v1/query_range?query={job=\"salon-ai-structured\"}&start=$(date -d '1 hour ago' +%s)000000000&end=$(date +%s)000000000" | jq '.data.result | length' 2>/dev/null || echo "Query failed or no jq"

echo ""
echo "8. Checking Grafana datasource..."
echo "----------------------------------"
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Grafana is responding"
    echo "Testing Grafana Loki datasource:"
    curl -s -u admin:admin "http://localhost:3001/api/datasources" | jq '.[] | select(.type=="loki") | {name: .name, url: .url, access: .access}' 2>/dev/null || echo "Datasource check failed or no jq"
else
    echo "❌ Grafana is not responding"
fi

echo ""
echo "9. Summary and Recommendations..."
echo "==================================="
echo "If logs are not showing up, try:"
echo "1. Check if log files have content: ls -la salon_ai.log logs/"
echo "2. Restart Promtail: docker-compose restart promtail"
echo "3. Check Promtail config: docker-compose logs promtail"
echo "4. Verify Grafana datasource in UI: http://localhost:3001/connections/datasources"
echo "5. Try manual log query in Grafana Explore: {job=~\".*\"}"
echo ""
echo "Manual test commands:"
echo "- Generate logs: python test_logging.py"
echo "- Check Loki: curl http://localhost:3100/loki/api/v1/labels"
echo "- Check Promtail: curl http://localhost:9080/targets"
