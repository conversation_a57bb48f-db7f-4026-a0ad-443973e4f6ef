server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Scrape logs from the salon AI application log file
  - job_name: salon-ai-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: salon-ai
          __path__: /var/log/salon/salon_ai.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<logger>\S+) - (?P<level>\S+) - (?P<message>.*)'
      - labels:
          level:
          logger:
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'

  # Scrape Docker container logs for the salon AI application
  - job_name: salon-ai-container
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
        filters:
          - name: name
            values: ["salon_livekit_agent"]
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.+)'
        target_label: 'container_name'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'stream'
    pipeline_stages:
      - json:
          expressions:
            timestamp: time
            message: log
            stream: stream
      - labels:
          stream:
      - timestamp:
          source: timestamp
          format: RFC3339Nano

  # Scrape general application logs from the logs directory
  - job_name: salon-ai-app-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: salon-ai-app
          __path__: /var/log/salon/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<logger>\S+) - (?P<level>\S+) - (?P<message>.*)'
      - labels:
          level:
          logger:
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'

  # Scrape structured JSON logs for better querying
  - job_name: salon-ai-structured-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: salon-ai-structured
          __path__: /var/log/salon/salon_ai_structured.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            logger: logger
            module: module
            function: function
            line: line
            message: message
            service: service
            environment: environment
      - labels:
          level:
          logger:
          module:
          service:
          environment:
      - timestamp:
          source: timestamp
          format: Unix
