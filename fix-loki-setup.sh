#!/bin/bash

# Fix Loki setup script - resolves volume mounting issues

echo "🔧 Fixing Loki setup..."

# Stop all services first
echo "🛑 Stopping services..."
docker-compose down --remove-orphans

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p loki

# Create log files with proper permissions
echo "📝 Creating log files..."
touch salon_ai.log
touch logs/salon_ai_structured.log

# Set proper permissions
echo "🔐 Setting permissions..."
chmod 644 salon_ai.log
chmod 644 logs/salon_ai_structured.log
chmod 755 logs
chmod 755 loki

# Remove any problematic volumes
echo "🧹 Cleaning up volumes..."
docker volume prune -f

# Start services again
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services..."
sleep 15

# Check service status
echo "🔍 Checking services..."

if curl -s http://localhost:3100/ready > /dev/null; then
    echo "✅ Loki is ready"
else
    echo "⚠️  Loki is starting..."
fi

if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ <PERSON><PERSON> is ready"
else
    echo "⚠️  <PERSON><PERSON> is starting..."
fi

echo ""
echo "🎉 Setup fixed! Services should be running now."
echo "📊 Grafana: http://localhost:3001 (admin/admin)"
echo "🔍 Loki API: http://localhost:3100"
echo ""
echo "🧪 Test with: python test_logging.py"
