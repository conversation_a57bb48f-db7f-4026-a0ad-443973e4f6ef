@echo off
REM Fix Loki setup script - resolves volume mounting issues

echo 🔧 Fixing Loki setup...

REM Stop all services first
echo 🛑 Stopping services...
docker-compose down --remove-orphans

REM Create necessary directories
echo 📁 Creating directories...
if not exist logs mkdir logs
if not exist loki mkdir loki

REM Create log files
echo 📝 Creating log files...
if not exist salon_ai.log echo. > salon_ai.log
if not exist logs\salon_ai_structured.log echo. > logs\salon_ai_structured.log

REM Remove any problematic volumes
echo 🧹 Cleaning up volumes...
docker volume prune -f

REM Start services again
echo 🚀 Starting services...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services...
timeout /t 15 /nobreak >nul

REM Check service status
echo 🔍 Checking services...

curl -s http://localhost:3100/ready >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Loki is starting...
) else (
    echo ✅ Loki is ready
)

curl -s http://localhost:3001/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  <PERSON><PERSON> is starting...
) else (
    echo ✅ <PERSON>ana is ready
)

echo.
echo 🎉 Setup fixed! Services should be running now.
echo 📊 Grafana: http://localhost:3001 (admin/admin)
echo 🔍 Loki API: http://localhost:3100
echo.
echo 🧪 Test with: python test_logging.py

pause
