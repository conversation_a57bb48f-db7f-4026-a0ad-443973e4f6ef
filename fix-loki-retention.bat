@echo off
REM Quick fix for Loki retention configuration error

echo 🔧 Fixing Loki retention configuration...

REM Stop Loki service
echo 🛑 Stopping Loki...
docker-compose stop loki

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start Loki with updated config
echo 🚀 Starting Loki with fixed configuration...
docker-compose up -d loki

REM Wait for Loki to start
echo ⏳ Waiting for Loki to start...
timeout /t 10 /nobreak >nul

REM Check if Loki is ready
echo 🔍 Checking Loki status...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready!
        goto :loki_ready
    ) else (
        echo ⏳ Loki starting... (attempt %%i/10)
        timeout /t 3 /nobreak >nul
    )
)
:loki_ready

REM Check for any errors in logs
echo 📋 Checking for errors...
docker-compose logs loki --tail=20 | findstr /i error >nul
if not errorlevel 1 (
    echo ⚠️  Found errors in Loki logs. Check with: docker-compose logs loki
) else (
    echo ✅ No errors found in recent logs
)

REM Test Loki API
echo 🔍 Testing Loki API...
curl -s "http://localhost:3100/loki/api/v1/labels" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Loki API is responding
) else (
    echo ⚠️  Loki API not ready yet
)

echo.
echo 🎉 Loki retention configuration fixed!
echo 📊 You can now access <PERSON>ana at http://localhost:3001
echo 🧪 Test with: python test_logging.py

pause
