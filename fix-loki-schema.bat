@echo off
REM Fix Loki schema configuration - updates to v13 schema

echo 🔧 Fixing Loki schema configuration...

REM Stop all services
echo 🛑 Stopping services...
docker-compose down --remove-orphans

REM Remove Loki data volume to start fresh with new schema
echo 🧹 Cleaning up Loki data (required for schema change)...
docker volume rm voice-agent_loki_data 2>nul

REM Remove any existing Loki containers
echo 🗑️  Removing old containers...
docker container rm salon_loki 2>nul
docker container rm salon_promtail 2>nul

REM Create necessary directories
echo 📁 Creating directories...
if not exist logs mkdir logs
if not exist loki mkdir loki

REM Create log files
echo 📝 Creating log files...
if not exist salon_ai.log echo. > salon_ai.log
if not exist logs\salon_ai_structured.log echo. > logs\salon_ai_structured.log

REM Pull latest Loki image
echo 📥 Pulling latest Loki image...
docker pull grafana/loki:latest
docker pull grafana/promtail:latest

REM Start services with new configuration
echo 🚀 Starting services with updated schema...
docker-compose up -d

REM Wait for services to initialize
echo ⏳ Waiting for services to initialize...
timeout /t 20 /nobreak >nul

REM Check service status
echo 🔍 Checking services...

echo Checking Loki...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready
        goto :loki_ready
    ) else (
        echo ⏳ Loki starting... (attempt %%i/10)
        timeout /t 5 /nobreak >nul
    )
)
:loki_ready

echo Checking Grafana...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3001/api/health >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Grafana is ready
        goto :grafana_ready
    ) else (
        echo ⏳ Grafana starting... (attempt %%i/10)
        timeout /t 5 /nobreak >nul
    )
)
:grafana_ready

REM Check Loki labels endpoint to verify it's working
echo 🔍 Verifying Loki API...
curl -s "http://localhost:3100/loki/api/v1/labels" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Loki API is responding
) else (
    echo ⚠️  Loki API not ready yet
)

echo.
echo 🎉 Loki schema updated to v13!
echo 📊 Grafana: http://localhost:3001 (admin/admin)
echo 🔍 Loki API: http://localhost:3100
echo.
echo 🧪 Test with: python test_logging.py
echo.
echo 📋 If you see any issues, check logs with:
echo    docker-compose logs loki
echo    docker-compose logs promtail

pause
