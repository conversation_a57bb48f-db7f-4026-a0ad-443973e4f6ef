# Loki Logging Setup for Salon AI Voice Agent

This document describes the Loki logging infrastructure setup for the Salon AI Voice Agent application.

## Overview

The logging stack consists of:
- **Loki**: Log aggregation system for storing and querying logs
- **Promtail**: Log collector that ships logs to Loki
- **Grafana**: Visualization and querying interface for logs
- **Structured Logging**: JSON-formatted logs for better querying and analysis

## Architecture

```
Application Logs → Promtail → Loki → Grafana
```

## Components

### 1. Loki
- **Port**: 3100
- **Storage**: Local filesystem (`/loki` volume)
- **Configuration**: `loki/loki-config.yml`
- **Purpose**: Stores and indexes log data

### 2. Promtail
- **Configuration**: `loki/promtail-config.yml`
- **Purpose**: Collects logs from multiple sources:
  - Application log files (`salon_ai.log`)
  - Structured JSON logs (`logs/salon_ai_structured.log`)
  - Docker container logs
  - General application logs from `/logs` directory

### 3. Grafana Integration
- **Datasource**: Loki datasource configured in `grafana/provisioning/datasources/loki.yml`
- **Dashboard**: Pre-configured logs dashboard in `grafana/provisioning/dashboards/salon-ai-logs-dashboard.json`
- **Access**: Available at http://localhost:3001

## Log Sources

### Standard Logs
- **File**: `salon_ai.log`
- **Format**: Standard text format with timestamp, logger, level, and message
- **Purpose**: Human-readable logs for debugging

### Structured Logs
- **File**: `logs/salon_ai_structured.log`
- **Format**: JSON with structured fields:
  ```json
  {
    "timestamp": 1640995200.123,
    "level": "INFO",
    "logger": "salon_ai.main",
    "module": "main",
    "function": "startup",
    "line": 36,
    "message": "Application started successfully",
    "service": "salon-ai-voice-agent",
    "environment": "development",
    "event": "application_startup",
    "component": "database",
    "status": "success"
  }
  ```

### Docker Container Logs
- **Source**: Docker daemon logs for `salon_livekit_agent` container
- **Format**: JSON with Docker metadata

## Usage

### Starting the Stack
```bash
docker-compose up -d
```

This will start:
- Loki on port 3100
- Promtail (no exposed port)
- Grafana on port 3001 (with Loki datasource configured)

### Viewing Logs in Grafana
1. Open http://localhost:3001
2. Login with admin/admin
3. Navigate to the "Salon AI - Logs Dashboard"
4. Use the Explore section for custom log queries

### Log Queries

#### Basic Queries
```logql
# All logs from the application
{job="salon-ai-structured"}

# Error logs only
{job="salon-ai-structured"} |= "ERROR"

# Logs from specific module
{job="salon-ai-structured", module="main"}

# Logs with specific event type
{job="salon-ai-structured"} | json | event="application_startup"
```

#### Advanced Queries
```logql
# Count of logs by level over time
sum by (level) (count_over_time({job="salon-ai-structured"} [5m]))

# Rate of error logs
rate({job="salon-ai-structured"} |= "ERROR" [5m])

# Logs containing specific text
{job="salon-ai-structured"} |~ "customer.*appointment"
```

## Configuration Files

### Loki Configuration (`loki/loki-config.yml`)
- Configures storage, schema, and server settings
- Uses local filesystem storage
- Retention period can be configured

### Promtail Configuration (`loki/promtail-config.yml`)
- Defines log sources and parsing rules
- Handles both structured and unstructured logs
- Includes Docker container log collection

### Grafana Datasource (`grafana/provisioning/datasources/loki.yml`)
- Automatically configures Loki as a datasource
- Sets up connection to Loki service

## Structured Logging in Code

### Using the Logger
```python
from config.logging import get_logger

logger = get_logger('component_name')

# Basic logging
logger.info("Operation completed")

# Structured logging with extra fields
logger.info("User action performed", extra={
    "event": "user_action",
    "user_id": "12345",
    "action": "appointment_booking",
    "status": "success"
})

# Error logging with context
logger.error("Database connection failed", extra={
    "event": "database_error",
    "component": "database",
    "error_type": "ConnectionError",
    "retry_count": 3
})
```

### Best Practices
1. Use consistent field names across the application
2. Include relevant context in extra fields
3. Use structured logging for important events
4. Include error types and context for error logs
5. Use event types to categorize log entries

## Monitoring and Alerting

### Key Metrics to Monitor
- Error rate by component
- Log volume trends
- Response time patterns
- Application startup/shutdown events

### Setting Up Alerts
Grafana can be configured to send alerts based on log patterns:
- High error rates
- Application crashes
- Unusual log volume
- Specific error patterns

## Troubleshooting

### Common Issues
1. **Loki not receiving logs**: Check Promtail configuration and file paths
2. **Grafana can't connect to Loki**: Verify network connectivity and Loki service status
3. **Missing structured logs**: Ensure application is using the new logging configuration
4. **Permission issues**: Check file permissions for log directories

### Debugging Commands
```bash
# Check Loki status
curl http://localhost:3100/ready

# Check Promtail targets
curl http://localhost:9080/targets

# View Loki metrics
curl http://localhost:3100/metrics

# Check log file permissions
ls -la logs/
```

## Performance Considerations

### Log Retention
- Configure retention policies in Loki configuration
- Monitor disk usage for log storage
- Consider log rotation for application files

### Query Performance
- Use label filters before text searches
- Limit time ranges for large queries
- Use metric queries for aggregations

### Resource Usage
- Monitor Loki memory usage
- Configure appropriate limits in docker-compose
- Consider using external storage for production

## Security

### Access Control
- Grafana authentication is enabled (admin/admin)
- Loki has no authentication by default (suitable for internal use)
- Consider adding authentication for production deployments

### Log Content
- Avoid logging sensitive information (passwords, tokens)
- Use structured logging to control what gets logged
- Consider log sanitization for PII data
