@echo off
REM Fix Loki configuration parsing error

echo 🔧 Fixing Loki configuration...

REM Stop Loki service
echo 🛑 Stopping Loki...
docker-compose stop loki

REM Remove any problematic Loki data
echo 🧹 Cleaning Loki data...
docker volume rm voice-agent_loki_data 2>nul

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start Loki with simplified config
echo 🚀 Starting Loki with simplified configuration...
docker-compose up -d loki

REM Wait for <PERSON> to start
echo ⏳ Waiting for <PERSON> to start...
timeout /t 15 /nobreak >nul

REM Check if <PERSON> is ready
echo 🔍 Checking Loki status...
for /l %%i in (1,1,15) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready!
        goto :loki_ready
    ) else (
        echo ⏳ Loki starting... (attempt %%i/15)
        timeout /t 3 /nobreak >nul
    )
)
:loki_ready

REM Check for any errors in logs
echo 📋 Checking for configuration errors...
docker-compose logs loki --tail=10 | findstr /i "error failed" >nul
if not errorlevel 1 (
    echo ⚠️  Found errors in Loki logs:
    docker-compose logs loki --tail=10
    echo.
    echo 💡 Try running: docker-compose logs loki
) else (
    echo ✅ No configuration errors found
)

REM Test Loki API
echo 🔍 Testing Loki API...
curl -s "http://localhost:3100/loki/api/v1/labels" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Loki API is responding
) else (
    echo ⚠️  Loki API not ready yet, but may still be starting
)

REM Start Promtail if it's not running
echo 🔄 Ensuring Promtail is running...
docker-compose up -d promtail

echo.
echo 🎉 Loki configuration fixed!
echo 📊 Grafana: http://localhost:3001 (admin/admin)
echo 🔍 Loki API: http://localhost:3100
echo.
echo 🧪 Test logging with: python test_logging.py
echo 📋 Check logs with: docker-compose logs loki

pause
