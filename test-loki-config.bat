@echo off
REM Test Loki configuration validity

echo 🔍 Testing Loki configuration...

REM Stop Loki first
echo 🛑 Stopping Loki...
docker-compose stop loki

REM Remove any existing Loki data
echo 🧹 Cleaning Loki data...
docker volume rm voice-agent_loki_data 2>nul

REM Test the configuration by starting Loki
echo 🚀 Testing Loki with current configuration...
docker-compose up -d loki

REM Wait and check for errors
echo ⏳ Waiting for Loki to start...
timeout /t 10 /nobreak >nul

REM Check for configuration errors
echo 📋 Checking for configuration errors...
docker-compose logs loki --tail=20 | findstr /i "failed parsing yaml unmarshal error" >nul
if not errorlevel 1 (
    echo ❌ Configuration errors found:
    docker-compose logs loki --tail=20 | findstr /i "failed parsing yaml unmarshal error"
    echo.
    echo 💡 Full logs:
    docker-compose logs loki --tail=30
    pause
    exit /b 1
) else (
    echo ✅ No configuration errors found
)

REM Test if Loki is ready
echo 🔍 Testing Loki readiness...
for /l %%i in (1,1,15) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready and responding!
        goto :loki_ready
    ) else (
        echo ⏳ Waiting for Loki... (%%i/15)
        timeout /t 2 /nobreak >nul
    )
)
:loki_ready

REM Test API endpoints
echo 🔍 Testing Loki API endpoints...

REM Test labels endpoint
curl -s "http://localhost:3100/loki/api/v1/labels" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Labels API working
) else (
    echo ⚠️  Labels API not ready
)

REM Test metrics endpoint
curl -s "http://localhost:3100/metrics" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Metrics API working
) else (
    echo ⚠️  Metrics API not ready
)

echo.
echo 🎉 Loki configuration test completed!
echo 📊 Loki is running at: http://localhost:3100
echo 🔍 Ready endpoint: http://localhost:3100/ready
echo 📋 Labels endpoint: http://localhost:3100/loki/api/v1/labels
echo.
echo 🚀 Next step: Run .\fix-logging-issues.bat to complete the setup

pause
