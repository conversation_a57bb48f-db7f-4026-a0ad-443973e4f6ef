#!/bin/bash

# Fix Loki schema configuration - updates to v13 schema

echo "🔧 Fixing Loki schema configuration..."

# Stop all services
echo "🛑 Stopping services..."
docker-compose down --remove-orphans

# Remove Loki data volume to start fresh with new schema
echo "🧹 Cleaning up Loki data (required for schema change)..."
docker volume rm voice-agent_loki_data 2>/dev/null || true

# Remove any existing Loki containers
echo "🗑️  Removing old containers..."
docker container rm salon_loki 2>/dev/null || true
docker container rm salon_promtail 2>/dev/null || true

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p loki

# Create log files with proper permissions
echo "📝 Creating log files..."
touch salon_ai.log
touch logs/salon_ai_structured.log

# Set proper permissions
echo "🔐 Setting permissions..."
chmod 644 salon_ai.log
chmod 644 logs/salon_ai_structured.log
chmod 755 logs
chmod 755 loki

# Pull latest Loki image
echo "📥 Pulling latest Loki image..."
docker pull grafana/loki:latest
docker pull grafana/promtail:latest

# Start services with new configuration
echo "🚀 Starting services with updated schema..."
docker-compose up -d

# Wait for services to initialize
echo "⏳ Waiting for services to initialize..."
sleep 20

# Check service status
echo "🔍 Checking services..."

# Check Loki
echo "Checking Loki..."
for i in {1..10}; do
    if curl -s http://localhost:3100/ready > /dev/null; then
        echo "✅ Loki is ready"
        break
    else
        echo "⏳ Loki starting... (attempt $i/10)"
        sleep 5
    fi
done

# Check Grafana
echo "Checking Grafana..."
for i in {1..10}; do
    if curl -s http://localhost:3001/api/health > /dev/null; then
        echo "✅ Grafana is ready"
        break
    else
        echo "⏳ Grafana starting... (attempt $i/10)"
        sleep 5
    fi
done

# Check Loki labels endpoint to verify it's working
echo "🔍 Verifying Loki API..."
if curl -s "http://localhost:3100/loki/api/v1/labels" > /dev/null; then
    echo "✅ Loki API is responding"
else
    echo "⚠️  Loki API not ready yet"
fi

echo ""
echo "🎉 Loki schema updated to v13!"
echo "📊 Grafana: http://localhost:3001 (admin/admin)"
echo "🔍 Loki API: http://localhost:3100"
echo ""
echo "🧪 Test with: python test_logging.py"
echo ""
echo "📋 If you see any issues, check logs with:"
echo "   docker-compose logs loki"
echo "   docker-compose logs promtail"
