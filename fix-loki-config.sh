#!/bin/bash

# Fix Loki configuration parsing error

echo "🔧 Fixing Loki configuration..."

# Stop Loki service
echo "🛑 Stopping Loki..."
docker-compose stop loki

# Remove any problematic Loki data
echo "🧹 Cleaning Loki data..."
docker volume rm voice-agent_loki_data 2>/dev/null || true

# Wait a moment
sleep 2

# Start Loki with simplified config
echo "🚀 Starting Loki with simplified configuration..."
docker-compose up -d loki

# Wait for <PERSON> to start
echo "⏳ Waiting for Loki to start..."
sleep 15

# Check if Loki is ready
echo "🔍 Checking Loki status..."
for i in {1..15}; do
    if curl -s http://localhost:3100/ready > /dev/null 2>&1; then
        echo "✅ Loki is ready!"
        break
    else
        echo "⏳ Loki starting... (attempt $i/15)"
        sleep 3
    fi
done

# Check for any errors in logs
echo "📋 Checking for configuration errors..."
if docker-compose logs loki --tail=10 | grep -i "error\|failed"; then
    echo "⚠️  Found errors in Loki logs:"
    docker-compose logs loki --tail=10
    echo ""
    echo "💡 Try running: docker-compose logs loki"
else
    echo "✅ No configuration errors found"
fi

# Test Loki API
echo "🔍 Testing Loki API..."
if curl -s "http://localhost:3100/loki/api/v1/labels" > /dev/null 2>&1; then
    echo "✅ Loki API is responding"
else
    echo "⚠️  Loki API not ready yet, but may still be starting"
fi

# Start Promtail if it's not running
echo "🔄 Ensuring Promtail is running..."
docker-compose up -d promtail

echo ""
echo "🎉 Loki configuration fixed!"
echo "📊 Grafana: http://localhost:3001 (admin/admin)"
echo "🔍 Loki API: http://localhost:3100"
echo ""
echo "🧪 Test logging with: python test_logging.py"
echo "📋 Check logs with: docker-compose logs loki"
