#!/bin/bash

# Quick fix for Loki YAML configuration error

echo "🔧 Fixing Loki YAML configuration..."

# Stop Loki
echo "🛑 Stopping Loki..."
docker-compose stop loki

# Wait a moment
sleep 2

# Start Loki with corrected config
echo "🚀 Starting Loki with fixed YAML..."
docker-compose up -d loki

# Wait for Loki to start
echo "⏳ Waiting for Loki..."
sleep 15

# Check for configuration errors
echo "📋 Checking for YAML errors..."
if docker-compose logs loki --tail=10 | grep -i "failed parsing\|yaml\|unmarshal"; then
    echo "❌ Still have YAML errors:"
    docker-compose logs loki --tail=10
    echo ""
    echo "💡 Check the configuration manually with:"
    echo "   docker-compose logs loki"
else
    echo "✅ No YAML parsing errors found"
fi

# Test if Loki is ready
echo "🔍 Testing Loki readiness..."
for i in {1..10}; do
    if curl -s http://localhost:3100/ready > /dev/null; then
        echo "✅ Loki is ready!"
        break
    else
        echo "⏳ Loki starting... ($i/10)"
        sleep 3
    fi
done

# Test API
echo "🔍 Testing Loki API..."
if curl -s "http://localhost:3100/loki/api/v1/labels" > /dev/null; then
    echo "✅ Loki API is responding"
    echo "Available labels:"
    curl -s "http://localhost:3100/loki/api/v1/labels" | jq . 2>/dev/null || curl -s "http://localhost:3100/loki/api/v1/labels"
else
    echo "⚠️  Loki API not ready yet"
fi

echo ""
echo "🎉 Loki YAML configuration fixed!"
echo "📊 Continue with: ./fix-logging-issues.sh"
