@echo off
REM Quick fix for Loki YAML configuration error

echo 🔧 Fixing Loki YAML configuration...

REM Stop Loki
echo 🛑 Stopping Loki...
docker-compose stop loki

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start Loki with corrected config
echo 🚀 Starting Loki with fixed YAML...
docker-compose up -d loki

REM Wait for <PERSON> to start
echo ⏳ Waiting for Loki...
timeout /t 15 /nobreak >nul

REM Check for configuration errors
echo 📋 Checking for YAML errors...
docker-compose logs loki --tail=10 | findstr /i "failed parsing yaml unmarshal" >nul
if not errorlevel 1 (
    echo ❌ Still have YAML errors:
    docker-compose logs loki --tail=10
    echo.
    echo 💡 Check the configuration manually with:
    echo    docker-compose logs loki
) else (
    echo ✅ No YAML parsing errors found
)

REM Test if Loki is ready
echo 🔍 Testing Loki readiness...
for /l %%i in (1,1,10) do (
    curl -s http://localhost:3100/ready >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Loki is ready!
        goto :loki_ready
    ) else (
        echo ⏳ Loki starting... (%%i/10)
        timeout /t 3 /nobreak >nul
    )
)
:loki_ready

REM Test API
echo 🔍 Testing Loki API...
curl -s "http://localhost:3100/loki/api/v1/labels" >nul 2>&1
if not errorlevel 1 (
    echo ✅ Loki API is responding
    echo Available labels:
    curl -s "http://localhost:3100/loki/api/v1/labels"
) else (
    echo ⚠️  Loki API not ready yet
)

echo.
echo 🎉 Loki YAML configuration fixed!
echo 📊 Continue with: .\fix-logging-issues.bat

pause
