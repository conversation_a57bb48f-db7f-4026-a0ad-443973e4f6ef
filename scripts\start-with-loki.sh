#!/bin/bash

# Startup script for Salon AI Voice Agent with <PERSON> logging
# This script ensures all necessary directories exist and starts the services

set -e

echo "🚀 Starting Salon AI Voice Agent with Loki Logging..."

# Create necessary directories
echo "📁 Creating log directories..."
mkdir -p logs
mkdir -p loki
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# Set proper permissions for log directories
echo "🔐 Setting permissions..."
chmod 755 logs
chmod 755 loki

# Create empty log files if they don't exist
touch salon_ai.log
touch logs/salon_ai_structured.log

# Ensure proper ownership and permissions
chmod 644 salon_ai.log
chmod 644 logs/salon_ai_structured.log

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down --remove-orphans

# Pull latest images
echo "📥 Pulling latest images..."
docker-compose pull

# Start the services
echo "🏃 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check Loki
if curl -s http://localhost:3100/ready > /dev/null; then
    echo "✅ Loki is ready"
else
    echo "⚠️  Loki is not ready yet, it may take a few more seconds"
fi

# Check Grafana
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Grafana is ready"
else
    echo "⚠️  Grafana is not ready yet, it may take a few more seconds"
fi

# Check Prometheus
if curl -s http://localhost:9090/-/ready > /dev/null; then
    echo "✅ Prometheus is ready"
else
    echo "⚠️  Prometheus is not ready yet, it may take a few more seconds"
fi

echo ""
echo "🎉 Services started successfully!"
echo ""
echo "📊 Access URLs:"
echo "   Grafana:    http://localhost:3001 (admin/admin)"
echo "   Prometheus: http://localhost:9090"
echo "   Loki API:   http://localhost:3100"
echo ""
echo "📋 Quick Start:"
echo "   1. Open Grafana at http://localhost:3001"
echo "   2. Login with admin/admin"
echo "   3. Go to 'Salon AI - Logs Dashboard' to view logs"
echo "   4. Use Explore to run custom log queries"
echo ""
echo "🧪 Test Logging:"
echo "   Run: python test_logging.py"
echo ""
echo "📖 Documentation:"
echo "   See: docs/LOKI_SETUP.md"
echo ""
echo "🔍 View logs:"
echo "   docker-compose logs -f [service-name]"
echo ""

# Optional: Run test logging if requested
if [ "$1" = "--test" ]; then
    echo "🧪 Running logging test..."
    python test_logging.py
fi
