# Docker Compose override for development
# This file provides additional configuration for development environments
# It will be automatically loaded when running docker-compose commands

version: '3.8'

services:
  livekit-agent:
    volumes:
      # Mount the current directory for live code changes
      - .:/app
      # Ensure logs directory is properly mounted
      - ./logs:/app/logs
      - ./salon_ai.log:/app/salon_ai.log
    environment:
      # Enable debug logging in development
      - LOG_LEVEL=DEBUG
      - ENVIRONMENT=development

  promtail:
    volumes:
      # Mount local logs directory for easier access
      - ./logs:/var/log/salon:ro
      - ./salon_ai.log:/var/log/salon/salon_ai.log:ro
      # Mount Docker socket for container log collection
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro

  loki:
    ports:
      # Expose Loki port for direct API access during development
      - "3100:3100"
    environment:
      # Enable debug logging for Loki
      - LOKI_LOG_LEVEL=debug

  grafana:
    environment:
      # Enable development features
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
      - GF_LOG_LEVEL=debug
    volumes:
      # Mount provisioning directory for live updates
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
