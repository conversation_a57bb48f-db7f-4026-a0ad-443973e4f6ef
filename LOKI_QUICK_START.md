# Loki Logging - Quick Start Guide

This guide will help you quickly set up and start using Loki for log aggregation in the Salon AI Voice Agent.

## 🚀 Quick Setup

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Python environment with required packages

### 1. Install Required Python Packages
```bash
pip install python-json-logger==2.0.7 structlog==24.1.0
```

### 2. Start Services
**Linux/Mac:**
```bash
chmod +x scripts/start-with-loki.sh
./scripts/start-with-loki.sh
```

**Windows:**
```cmd
scripts\start-with-loki.bat
```

**If you encounter schema errors, use the schema fix script:**
**Linux/Mac:**
```bash
chmod +x fix-loki-schema.sh
./fix-loki-schema.sh
```

**Windows:**
```cmd
fix-loki-schema.bat
```

### 3. Verify Setup
- **Grafana**: http://localhost:3001 (admin/admin)
- **Loki API**: http://localhost:3100
- **Prometheus**: http://localhost:9090

## 📊 Viewing Logs

### In Grafana
1. Open http://localhost:3001
2. Login with `admin/admin`
3. Navigate to "Salon AI - Logs Dashboard"
4. View real-time logs and metrics

### Dashboard Panels
- **Log Levels Distribution**: Pie chart showing log level breakdown
- **Log Rate by Level**: Time series of log rates
- **Recent Logs**: Latest log entries
- **Error Logs**: Filtered error messages
- **Warning Logs**: Filtered warning messages

## 🧪 Test the Setup

Run the test script to generate sample logs:
```bash
python test_logging.py
```

This will generate various types of logs that you can view in Grafana.

## 🔍 Log Queries

### Basic Queries (in Grafana Explore)
```logql
# All application logs
{job="salon-ai-structured"}

# Error logs only
{job="salon-ai-structured"} |= "ERROR"

# Logs from specific component
{job="salon-ai-structured", component="database"}

# Logs with specific event
{job="salon-ai-structured"} | json | event="application_startup"
```

### Advanced Queries
```logql
# Error rate over time
rate({job="salon-ai-structured"} |= "ERROR" [5m])

# Count by log level
sum by (level) (count_over_time({job="salon-ai-structured"} [1h]))

# Logs containing customer interactions
{job="salon-ai-structured"} |~ "customer|appointment"
```

## 📝 Using Structured Logging in Code

```python
from config.logging import get_logger

logger = get_logger('my_component')

# Basic logging
logger.info("Operation completed")

# Structured logging with context
logger.info("User action performed", extra={
    "event": "user_action",
    "user_id": "12345",
    "action": "appointment_booking",
    "status": "success",
    "duration_ms": 150
})

# Error logging with details
logger.error("Database operation failed", extra={
    "event": "database_error",
    "component": "database",
    "error_type": "ConnectionTimeout",
    "retry_count": 3,
    "table": "appointments"
})
```

## 🛠️ Troubleshooting

### Services Not Starting
```bash
# Check Docker status
docker info

# View service logs
docker-compose logs loki
docker-compose logs promtail
docker-compose logs grafana

# Restart services
docker-compose restart
```

### No Logs in Grafana
1. Check if log files exist: `ls -la logs/`
2. Verify Promtail is running: `docker-compose ps promtail`
3. Check Promtail logs: `docker-compose logs promtail`
4. Verify Loki is receiving data: `curl http://localhost:3100/loki/api/v1/label`

### Permission Issues
```bash
# Fix log directory permissions
chmod 755 logs/
chmod 644 logs/*.log
```

## 📚 Next Steps

1. **Read the full documentation**: `docs/LOKI_SETUP.md`
2. **Customize dashboards**: Create your own Grafana dashboards
3. **Set up alerts**: Configure Grafana alerts for error conditions
4. **Optimize queries**: Learn advanced LogQL for better log analysis

## 🔧 Configuration Files

- **Loki**: `loki/loki-config.yml`
- **Promtail**: `loki/promtail-config.yml`
- **Grafana Datasource**: `grafana/provisioning/datasources/loki.yml`
- **Dashboard**: `grafana/provisioning/dashboards/salon-ai-logs-dashboard.json`

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker Compose logs
3. Consult the full documentation in `docs/LOKI_SETUP.md`
