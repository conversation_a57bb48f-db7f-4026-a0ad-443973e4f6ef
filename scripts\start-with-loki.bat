@echo off
REM Startup script for Salon AI Voice Agent with Loki logging (Windows)

echo 🚀 Starting Salon AI Voice Agent with Loki Logging...

REM Create necessary directories
echo 📁 Creating log directories...
if not exist logs mkdir logs
if not exist loki mkdir loki
if not exist grafana\provisioning\datasources mkdir grafana\provisioning\datasources
if not exist grafana\provisioning\dashboards mkdir grafana\provisioning\dashboards

REM Create empty log files if they don't exist
if not exist salon_ai.log (
    echo. > salon_ai.log
    echo Created salon_ai.log
)
if not exist logs\salon_ai_structured.log (
    echo. > logs\salon_ai_structured.log
    echo Created logs\salon_ai_structured.log
)

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose first.
    pause
    exit /b 1
)

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker-compose down --remove-orphans

REM Pull latest images
echo 📥 Pulling latest images...
docker-compose pull

REM Start the services
echo 🏃 Starting services...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service health
echo 🔍 Checking service health...

REM Check Loki
curl -s http://localhost:3100/ready >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Loki is not ready yet, it may take a few more seconds
) else (
    echo ✅ Loki is ready
)

REM Check Grafana
curl -s http://localhost:3001/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Grafana is not ready yet, it may take a few more seconds
) else (
    echo ✅ Grafana is ready
)

REM Check Prometheus
curl -s http://localhost:9090/-/ready >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Prometheus is not ready yet, it may take a few more seconds
) else (
    echo ✅ Prometheus is ready
)

echo.
echo 🎉 Services started successfully!
echo.
echo 📊 Access URLs:
echo    Grafana:    http://localhost:3001 (admin/admin)
echo    Prometheus: http://localhost:9090
echo    Loki API:   http://localhost:3100
echo.
echo 📋 Quick Start:
echo    1. Open Grafana at http://localhost:3001
echo    2. Login with admin/admin
echo    3. Go to 'Salon AI - Logs Dashboard' to view logs
echo    4. Use Explore to run custom log queries
echo.
echo 🧪 Test Logging:
echo    Run: python test_logging.py
echo.
echo 📖 Documentation:
echo    See: docs\LOKI_SETUP.md
echo.
echo 🔍 View logs:
echo    docker-compose logs -f [service-name]
echo.

REM Optional: Run test logging if requested
if "%1"=="--test" (
    echo 🧪 Running logging test...
    python test_logging.py
)

pause
