#!/bin/bash

# Comprehensive fix for all logging issues

echo "🔧 Fixing all logging issues..."

# Stop all services
echo "🛑 Stopping all services..."
docker-compose down --remove-orphans

# Clean up Loki data to fix timestamp issues
echo "🧹 Cleaning up Loki data (fixes timestamp issues)..."
docker volume rm voice-agent_loki_data 2>/dev/null || true

# Ensure log files exist and are accessible
echo "📝 Setting up log files..."
mkdir -p logs
touch salon_ai.log
touch logs/salon_ai_structured.log
chmod 644 salon_ai.log
chmod 644 logs/salon_ai_structured.log

# Start services with fixed configuration
echo "🚀 Starting services with fixes..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to initialize..."
sleep 20

# Check Loki
echo "🔍 Checking Loki..."
for i in {1..10}; do
    if curl -s http://localhost:3100/ready > /dev/null; then
        echo "✅ Loki is ready"
        break
    else
        echo "⏳ Loki starting... ($i/10)"
        sleep 3
    fi
done

# Check Promtail
echo "🔍 Checking Promtail..."
for i in {1..10}; do
    if curl -s http://localhost:9080/targets > /dev/null; then
        echo "✅ Promtail is ready"
        break
    else
        echo "⏳ Promtail starting... ($i/10)"
        sleep 3
    fi
done

# Check Grafana
echo "🔍 Checking Grafana..."
for i in {1..10}; do
    if curl -s http://localhost:3001/api/health > /dev/null; then
        echo "✅ Grafana is ready"
        break
    else
        echo "⏳ Grafana starting... ($i/10)"
        sleep 3
    fi
done

# Generate test logs
echo "🧪 Generating test logs..."
python test_logging.py

# Wait for logs to be processed
echo "⏳ Waiting for logs to be processed..."
sleep 10

# Check if logs are in Loki
echo "🔍 Checking if logs are in Loki..."
LOGS_COUNT=$(curl -s "http://localhost:3100/loki/api/v1/query_range?query={job=\"salon-ai-structured\"}&start=$(date -d '10 minutes ago' +%s)000000000&end=$(date +%s)000000000" | jq '.data.result | length' 2>/dev/null || echo "0")

if [ "$LOGS_COUNT" -gt 0 ]; then
    echo "✅ Found $LOGS_COUNT log streams in Loki"
else
    echo "⚠️  No logs found yet, checking for errors..."
    echo "Promtail errors:"
    docker-compose logs promtail --tail=5 | grep -i error || echo "No errors found"
fi

# Check available labels
echo "📋 Available labels in Loki:"
curl -s "http://localhost:3100/loki/api/v1/labels" | jq . 2>/dev/null || curl -s "http://localhost:3100/loki/api/v1/labels"

echo ""
echo "🎉 Logging fixes applied!"
echo ""
echo "📊 Access URLs:"
echo "   Grafana:    http://localhost:3001 (admin/admin)"
echo "   Loki API:   http://localhost:3100"
echo "   Promtail:   http://localhost:9080"
echo ""
echo "🔍 To check logs in Grafana:"
echo "   1. Go to http://localhost:3001"
echo "   2. Navigate to Explore"
echo "   3. Select Loki datasource"
echo "   4. Try query: {job=\"salon-ai-structured\"}"
echo ""
echo "🧪 Generate more test logs: python test_logging.py"
