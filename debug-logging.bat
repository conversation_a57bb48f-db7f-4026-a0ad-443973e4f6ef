@echo off
REM Debug script to troubleshoot logging pipeline

echo 🔍 Debugging Loki Logging Pipeline...
echo ==================================

REM Check if services are running
echo 1. Checking service status...
echo ------------------------------
docker-compose ps

echo.
echo 2. Checking log files exist...
echo ------------------------------
if exist salon_ai.log (
    for /f %%i in ('find /c /v "" ^< salon_ai.log') do echo ✅ salon_ai.log exists (%%i lines)
    echo Last 3 lines:
    powershell "Get-Content salon_ai.log | Select-Object -Last 3"
) else (
    echo ❌ salon_ai.log not found
)

if exist logs\salon_ai_structured.log (
    for /f %%i in ('find /c /v "" ^< logs\salon_ai_structured.log') do echo ✅ logs\salon_ai_structured.log exists (%%i lines)
    echo Last 3 lines:
    powershell "Get-Content logs\salon_ai_structured.log | Select-Object -Last 3"
) else (
    echo ❌ logs\salon_ai_structured.log not found
)

echo.
echo 3. Testing Loki API...
echo ---------------------
REM Test Loki ready endpoint
curl -s http://localhost:3100/ready >nul 2>&1
if not errorlevel 1 (
    echo ✅ Loki is ready
) else (
    echo ❌ Loki is not ready
)

echo Checking Loki labels:
curl -s "http://localhost:3100/loki/api/v1/labels"

echo.
echo 4. Checking Promtail status...
echo ------------------------------
curl -s http://localhost:9080/targets >nul 2>&1
if not errorlevel 1 (
    echo ✅ Promtail is responding
    echo Promtail targets:
    curl -s http://localhost:9080/targets
) else (
    echo ❌ Promtail is not responding
)

echo.
echo 5. Checking recent logs from services...
echo ----------------------------------------
echo Loki logs (last 10 lines):
docker-compose logs loki --tail=10

echo.
echo Promtail logs (last 10 lines):
docker-compose logs promtail --tail=10

echo.
echo 6. Testing log ingestion...
echo ---------------------------
REM Generate a test log entry
powershell "Add-Content salon_ai.log \"$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss,fff') - test.debug - INFO - Debug test log entry\""
powershell "Add-Content logs\salon_ai_structured.log '{\"timestamp\": '$(Get-Date -UFormat %%s)', \"level\": \"INFO\", \"logger\": \"test.debug\", \"message\": \"Debug structured test log\", \"service\": \"salon-ai-voice-agent\"}'"

echo Added test log entries

REM Wait a moment for Promtail to pick them up
timeout /t 5 /nobreak >nul

echo.
echo 7. Querying Loki for recent logs...
echo -----------------------------------
echo Querying all jobs:
curl -s "http://localhost:3100/loki/api/v1/query_range?query={job=~\".*\"}"

echo.
echo 8. Checking Grafana datasource...
echo ----------------------------------
curl -s http://localhost:3001/api/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ Grafana is responding
    echo Testing Grafana Loki datasource:
    curl -s -u admin:admin "http://localhost:3001/api/datasources"
) else (
    echo ❌ Grafana is not responding
)

echo.
echo 9. Summary and Recommendations...
echo ===================================
echo If logs are not showing up, try:
echo 1. Check if log files have content: dir salon_ai.log logs\
echo 2. Restart Promtail: docker-compose restart promtail
echo 3. Check Promtail config: docker-compose logs promtail
echo 4. Verify Grafana datasource in UI: http://localhost:3001/connections/datasources
echo 5. Try manual log query in Grafana Explore: {job=~".*"}
echo.
echo Manual test commands:
echo - Generate logs: python test_logging.py
echo - Check Loki: curl http://localhost:3100/loki/api/v1/labels
echo - Check Promtail: curl http://localhost:9080/targets

pause
