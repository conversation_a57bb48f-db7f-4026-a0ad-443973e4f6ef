#!/bin/bash

# Quick fix for Loki retention configuration error

echo "🔧 Fixing Loki retention configuration..."

# Stop Loki service
echo "🛑 Stopping Loki..."
docker-compose stop loki

# Wait a moment
sleep 2

# Start Loki with updated config
echo "🚀 Starting Loki with fixed configuration..."
docker-compose up -d loki

# Wait for Loki to start
echo "⏳ Waiting for Loki to start..."
sleep 10

# Check if Loki is ready
echo "🔍 Checking Loki status..."
for i in {1..10}; do
    if curl -s http://localhost:3100/ready > /dev/null; then
        echo "✅ Loki is ready!"
        break
    else
        echo "⏳ Loki starting... (attempt $i/10)"
        sleep 3
    fi
done

# Check for any errors in logs
echo "📋 Checking for errors..."
if docker-compose logs loki --tail=20 | grep -i error; then
    echo "⚠️  Found errors in Loki logs. Check with: docker-compose logs loki"
else
    echo "✅ No errors found in recent logs"
fi

# Test Loki API
echo "🔍 Testing Loki API..."
if curl -s "http://localhost:3100/loki/api/v1/labels" > /dev/null; then
    echo "✅ Loki API is responding"
else
    echo "⚠️  Loki API not ready yet"
fi

echo ""
echo "🎉 Loki retention configuration fixed!"
echo "📊 You can now access Grafana at http://localhost:3001"
echo "🧪 Test with: python test_logging.py"
